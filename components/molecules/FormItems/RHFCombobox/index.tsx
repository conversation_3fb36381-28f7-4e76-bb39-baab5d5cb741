'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Controller, useFormContext, Control, FieldError } from 'react-hook-form';
import { Search, X, ChevronDown, ChevronUp, Check } from 'lucide-react';
import { cn } from '@/utils/cn';

export type ComboboxOption = {
  label: string;
  value: string;
  description?: string;
};

export type RHFComboboxProps = {
  name: string;
  label?: string;
  options: ComboboxOption[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  icon?: React.ReactNode;
  noOptionsMessage?: string;
  // Additional props for direct integration with React Hook Form
  control?: Control<any>;
  error?: FieldError;
};

export const RHFCombobox: React.FC<RHFComboboxProps> = ({
  name,
  label,
  options,
  placeholder = 'Select an option...',
  className,
  disabled = false,
  required = false,
  icon,
  noOptionsMessage = 'No options available',
  control: controlProp,
  error: errorProp,
}) => {
  // Use form context if control is not provided directly
  const formContext = useFormContext();
  const control = controlProp || formContext?.control;
  const error = errorProp || formContext?.formState?.errors?.[name];
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focus input when dropdown opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  return (
    <div className={cn('space-y-2', className)}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          // Find the selected option for display
          const selectedOption = options.find(option => option.value === field.value);

          return (
            <div className="relative" ref={dropdownRef}>
              {/* Selected value display / trigger button */}
              <div
                className={cn(
                  'flex items-center justify-between w-full px-3 py-2.5 border rounded-lg shadow-sm',
                  'focus:outline-none focus:ring-2 transition-all duration-300',
                  error
                    ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500',
                  'hover:border-gray-300 cursor-pointer',
                  disabled && 'bg-gray-100 cursor-not-allowed opacity-75'
                )}
                onClick={() => !disabled && setIsOpen(!isOpen)}
                role="combobox"
                aria-expanded={isOpen}
                aria-haspopup="listbox"
                aria-controls={`${name}-options`}
              >
                <div className="flex items-center flex-1 min-w-0">
                  {icon && (
                    <span className="flex-shrink-0 text-gray-500 mr-2">
                      {icon}
                    </span>
                  )}
                  <span className="truncate">
                    {selectedOption ? selectedOption.label : placeholder}
                  </span>
                </div>
                <div className="flex-shrink-0 ml-1">
                  {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                </div>
              </div>

              {/* Dropdown */}
              {isOpen && (
                <div 
                  className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto dropdown-content"
                  id={`${name}-options`}
                  role="listbox"
                  style={{ 
                    maxHeight: '15rem', // Set a reasonable max height
                    position: 'absolute',
                    width: '100%',
                    overflow: 'auto',
                    // Remove overflow hidden from parent containers
                    zIndex: 50 // Higher z-index to ensure it appears above other elements
                  }}
                >
                  {/* Search input */}
                  <div className="sticky top-0 p-2 bg-white border-b border-gray-200">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search size={16} className="text-gray-400" />
                      </div>
                      <input
                        ref={inputRef}
                        type="text"
                        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Search..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      {searchTerm && (
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSearchTerm('');
                          }}
                        >
                          <X size={16} />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Options list */}
                  <ul className="py-1">
                    {filteredOptions.length > 0 ? (
                      filteredOptions.map((option) => (
                        <li
                          key={option.value}
                          className={cn(
                            'px-4 py-2 text-sm cursor-pointer flex items-center justify-between',
                            option.value === field.value
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700 hover:bg-gray-100'
                          )}
                          onClick={() => {
                            field.onChange(option.value);
                            setIsOpen(false);
                            setSearchTerm('');
                          }}
                          role="option"
                          aria-selected={option.value === field.value}
                        >
                          <span className="truncate">{option.label}</span>
                          {option.value === field.value && (
                            <Check size={16} className="text-blue-600 flex-shrink-0" />
                          )}
                        </li>
                      ))
                    ) : (
                      <li className="px-4 py-2 text-sm text-gray-500">{noOptionsMessage}</li>
                    )}
                  </ul>
                </div>
              )}

              {/* Error message */}
              {error && (
                <p className="mt-1 text-sm text-red-600">
                  {error.message as string}
                </p>
              )}
            </div>
          );
        }}
      />
    </div>
  );
};

export default RHFCombobox;